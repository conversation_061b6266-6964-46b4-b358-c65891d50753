/* Tamruh Design System - Dual Theme Support */

/* Google Fonts Import for Arabic Typography */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

/* Light Theme Variables (Default) */
:root {
  --primary-color: #6C5CE7;
  --secondary-color: #A8A5E6;
  --background: #F8F9FA; /* لون خلفية أفتح قليلاً */
  --card-bg: #FFFFFF;
  --text-color: #2D3748;
  --border-color: #E2E8F0;
  --shadow-color: rgba(0, 0, 0, 0.05);

  /* Theme transition */
  --theme-transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark Theme Variables */
.theme-dark {
  --primary-color: #6C5CE7;
  --secondary-color: #A8A5E6;
  --background: #121212;
  --card-bg: #1E1E1E;
  --text-color: #E2E8F0;
  --border-color: #2D3748;
  --shadow-color: rgba(0, 0, 0, 0.2);
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Tajawal', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background);
  color: var(--text-color);
  min-height: 100vh;
  font-size: 16px;
  line-height: 1.6;
  transition: var(--theme-transition);
}

#root {
  min-height: 100vh;
}